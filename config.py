#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow Agent 配置文件
"""

# RAGFlow 服务配置
RAGFLOW_CONFIG = {
    "api_key": "ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm",
    "base_url": "http://117.72.181.138:180/",
    "agent_id": "92a162f6710011f097860242ac130006"
}

# 对话配置
CHAT_CONFIG = {
    "stream": True,  # 是否使用流式输出
    "show_references": True,  # 是否显示引用信息
    "max_reference_content": 100,  # 引用内容最大显示长度
}

# 界面配置
UI_CONFIG = {
    "welcome_message": "🤖 RAGFlow Agent 对话程序",
    "user_prompt": "👤 您: ",
    "agent_prompt": "🤖 Agent: ",
    "exit_commands": ["quit", "exit", "q"],
    "help_commands": ["help", "h"],
    "clear_commands": ["clear", "cls"]
}
