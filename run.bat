@echo off
chcp 65001 >nul
echo ========================================
echo RAGFlow Agent 对话程序启动器
echo ========================================
echo.
echo 请选择要运行的程序:
echo [1] 测试连接
echo [2] 简化版对话程序
echo [3] 完整版对话程序  
echo [4] 配置版对话程序
echo [5] 安装依赖
echo [0] 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" (
    echo.
    echo 正在运行连接测试...
    python test_connection.py
    pause
    goto :start
)

if "%choice%"=="2" (
    echo.
    echo 正在启动简化版对话程序...
    python simple_agent_chat.py
    pause
    goto :start
)

if "%choice%"=="3" (
    echo.
    echo 正在启动完整版对话程序...
    python ragflow_agent_chat.py
    pause
    goto :start
)

if "%choice%"=="4" (
    echo.
    echo 正在启动配置版对话程序...
    python agent_chat_with_config.py
    pause
    goto :start
)

if "%choice%"=="5" (
    echo.
    echo 正在安装依赖...
    pip install ragflow-sdk
    echo.
    echo 依赖安装完成！
    pause
    goto :start
)

if "%choice%"=="0" (
    echo 再见！
    exit /b 0
)

echo 无效选择，请重新输入
pause
:start
cls
goto :eof
