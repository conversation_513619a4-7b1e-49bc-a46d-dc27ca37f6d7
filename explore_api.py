#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
探索RAGFlow SDK API的脚本
"""

from ragflow_sdk import RAGFlow

# 配置信息
API_KEY = "ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm"
BASE_URL = "http://117.72.181.138:180/"
AGENT_ID = "92a162f6710011f097860242ac130006"

def explore_ragflow_api():
    """探索RAGFlow API"""
    try:
        print("🔍 探索RAGFlow SDK API...")
        print(f"Base URL: {BASE_URL}")
        print("-" * 50)
        
        # 创建客户端
        rag_client = RAGFlow(api_key=API_KEY, base_url=BASE_URL)
        print("✓ RAGFlow客户端创建成功")
        
        # 获取所有可用方法
        print("\n📋 RAGFlow客户端可用方法:")
        methods = [method for method in dir(rag_client) if not method.startswith('_')]
        for i, method in enumerate(methods, 1):
            print(f"  {i:2d}. {method}")
        
        # 查找Agent相关方法
        agent_methods = [method for method in methods if 'agent' in method.lower()]
        print(f"\n🤖 Agent相关方法: {agent_methods}")
        
        # 查找Chat相关方法
        chat_methods = [method for method in methods if 'chat' in method.lower()]
        print(f"💬 Chat相关方法: {chat_methods}")
        
        # 尝试不同的方法
        print("\n🧪 尝试不同的API调用...")
        
        # 尝试1: list_chats (根据文档，这个方法应该存在)
        if hasattr(rag_client, 'list_chats'):
            print("✓ 找到 list_chats 方法")
            try:
                chats = rag_client.list_chats()
                print(f"  获取到 {len(chats)} 个聊天助手")
                for i, chat in enumerate(chats[:3], 1):  # 只显示前3个
                    chat_id = getattr(chat, 'id', 'Unknown')
                    chat_name = getattr(chat, 'name', 'Unknown')
                    print(f"    [{i}] ID: {chat_id}, 名称: {chat_name}")
            except Exception as e:
                print(f"  ✗ list_chats 调用失败: {e}")
        
        # 尝试2: 直接使用Agent ID
        print(f"\n🎯 尝试直接使用Agent ID: {AGENT_ID}")
        try:
            # 检查是否有Agent类
            from ragflow_sdk import Agent
            print("✓ 找到 Agent 类")
            
            # 尝试创建Agent实例
            agent = Agent(rag_client, AGENT_ID)
            print("✓ Agent实例创建成功")
            
            # 检查Agent的方法
            agent_instance_methods = [method for method in dir(agent) if not method.startswith('_')]
            print(f"Agent实例方法: {agent_instance_methods[:10]}...")  # 只显示前10个
            
            # 尝试创建会话
            if hasattr(agent, 'create_session'):
                print("✓ 找到 create_session 方法")
                session = agent.create_session()
                print(f"✓ 会话创建成功: {getattr(session, 'id', 'Unknown')}")
                
                # 尝试简单对话
                if hasattr(session, 'ask'):
                    print("✓ 找到 ask 方法，尝试对话...")
                    response = session.ask("你好", stream=False)
                    content = getattr(response, 'content', 'No content')
                    print(f"✓ 对话成功: {content[:100]}...")
                    
        except ImportError:
            print("✗ 无法导入 Agent 类")
        except Exception as e:
            print(f"✗ Agent操作失败: {e}")
        
        # 尝试3: 检查是否有其他获取Agent的方法
        print("\n🔍 检查其他可能的方法...")
        possible_methods = ['get_agent', 'agent', 'agents', 'list_agents', 'get_agents']
        for method_name in possible_methods:
            if hasattr(rag_client, method_name):
                print(f"✓ 找到方法: {method_name}")
                try:
                    method = getattr(rag_client, method_name)
                    if callable(method):
                        print(f"  {method_name} 是可调用的方法")
                except Exception as e:
                    print(f"  {method_name} 调用出错: {e}")
            else:
                print(f"✗ 未找到方法: {method_name}")
        
        print("\n" + "="*50)
        print("🎉 API探索完成！")
        
    except Exception as e:
        print(f"✗ 探索失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    explore_ragflow_api()
