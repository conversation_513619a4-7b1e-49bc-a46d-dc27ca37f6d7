#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版RAGFlow Agent对话程序
"""

from ragflow_sdk import RAGFlow

# 配置信息
API_KEY = "ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm"
BASE_URL = "http://117.72.181.138:180/"
AGENT_ID = "92a162f6710011f097860242ac130006"

def main():
    """主函数"""
    try:
        # 1. 初始化RAGFlow客户端
        print("正在连接RAGFlow服务...")
        rag_client = RAGFlow(api_key=API_KEY, base_url=BASE_URL)
        print("✓ 连接成功")
        
        # 2. 获取Agent
        print(f"正在获取Agent (ID: {AGENT_ID})...")
        agents = rag_client.list_agents(id=AGENT_ID)
        if not agents:
            print(f"✗ 未找到Agent ID: {AGENT_ID}")
            return
        
        agent = agents[0]
        print(f"✓ 获取Agent成功: {getattr(agent, 'title', 'Unknown')}")
        
        # 3. 创建会话
        print("正在创建对话会话...")
        session = agent.create_session()
        print(f"✓ 会话创建成功 (ID: {session.id})")
        
        # 4. 开始对话
        print("\n" + "="*50)
        print("🤖 RAGFlow Agent 对话开始")
        print("输入 'quit' 退出")
        print("="*50)
        
        # 显示欢迎消息
        if hasattr(session, 'message') and session.message:
            for msg in session.message:
                if msg.get('role') == 'assistant':
                    print(f"\n🤖 Agent: {msg.get('content', '')}")
        
        # 对话循环
        while True:
            # 获取用户输入
            question = input("\n👤 您: ").strip()
            
            # 检查退出命令
            if question.lower() in ['quit', 'exit', 'q']:
                print("\n👋 再见！")
                break
            
            if not question:
                continue
            
            # 向Agent提问（流式输出）
            print("\n🤖 Agent: ", end="", flush=True)
            content = ""
            try:
                for response in session.ask(question=question, stream=True):
                    new_content = response.content[len(content):]
                    print(new_content, end="", flush=True)
                    content = response.content
                print()  # 换行
                
                # 显示引用信息（如果有）
                if hasattr(response, 'reference') and response.reference:
                    print("\n📚 参考资料:")
                    for i, ref in enumerate(response.reference, 1):
                        doc_name = getattr(ref, 'document_name', 'Unknown')
                        similarity = getattr(ref, 'similarity', 0)
                        print(f"  [{i}] {doc_name} (相似度: {similarity:.3f})")
                        
            except Exception as e:
                print(f"\n✗ 提问失败: {e}")
    
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"✗ 程序运行出错: {e}")

if __name__ == "__main__":
    main()
