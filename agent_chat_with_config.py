#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用配置文件的RAGFlow Agent对话程序
"""

import sys
from ragflow_sdk import RAGFlow
from config import RAGFLOW_CONFIG, CHAT_CONFIG, UI_CONFIG

class RAGFlowAgentChat:
    """RAGFlow Agent对话类"""
    
    def __init__(self):
        """初始化"""
        self.rag_client = None
        self.agent = None
        self.session = None
        
    def connect(self):
        """连接到RAGFlow服务"""
        try:
            print("正在连接到RAGFlow服务...")
            self.rag_client = RAGFlow(
                api_key=RAGFLOW_CONFIG["api_key"],
                base_url=RAGFLOW_CONFIG["base_url"]
            )
            print("✓ 成功连接到RAGFlow服务")
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def get_agent(self):
        """获取Agent"""
        try:
            agent_id = RAGFLOW_CONFIG["agent_id"]
            print(f"正在获取Agent (ID: {agent_id})...")
            
            agents = self.rag_client.list_agents(id=agent_id)
            if not agents:
                print(f"✗ 未找到Agent ID: {agent_id}")
                return False
            
            self.agent = agents[0]
            agent_title = getattr(self.agent, 'title', 'Unknown')
            print(f"✓ 成功获取Agent: {agent_title}")
            return True
        except Exception as e:
            print(f"✗ 获取Agent失败: {e}")
            return False
    
    def create_session(self):
        """创建对话会话"""
        try:
            print("正在创建对话会话...")
            self.session = self.agent.create_session()
            print(f"✓ 会话创建成功 (ID: {self.session.id})")
            return True
        except Exception as e:
            print(f"✗ 创建会话失败: {e}")
            return False
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("\n" + "="*60)
        print(UI_CONFIG["welcome_message"])
        print("="*60)
        print("命令说明:")
        print(f"  退出: {', '.join(UI_CONFIG['exit_commands'])}")
        print(f"  帮助: {', '.join(UI_CONFIG['help_commands'])}")
        print(f"  清屏: {', '.join(UI_CONFIG['clear_commands'])}")
        print("="*60)
        
        # 显示Agent的欢迎消息
        if hasattr(self.session, 'message') and self.session.message:
            for msg in self.session.message:
                if msg.get('role') == 'assistant':
                    print(f"\n{UI_CONFIG['agent_prompt']}{msg.get('content', '')}")
    
    def show_references(self, references):
        """显示引用信息"""
        if not references or not CHAT_CONFIG["show_references"]:
            return
        
        print("\n📚 参考资料:")
        max_content = CHAT_CONFIG["max_reference_content"]
        
        for i, ref in enumerate(references, 1):
            doc_name = getattr(ref, 'document_name', 'Unknown')
            similarity = getattr(ref, 'similarity', 0)
            print(f"  [{i}] {doc_name} (相似度: {similarity:.3f})")
            
            if hasattr(ref, 'content'):
                content = ref.content
                if len(content) > max_content:
                    content = content[:max_content] + "..."
                print(f"      内容: {content}")
    
    def ask_question(self, question):
        """向Agent提问"""
        try:
            if CHAT_CONFIG["stream"]:
                # 流式输出
                print(f"\n{UI_CONFIG['agent_prompt']}", end="", flush=True)
                content = ""
                
                for response in self.session.ask(question=question, stream=True):
                    new_content = response.content[len(content):]
                    print(new_content, end="", flush=True)
                    content = response.content
                
                print()  # 换行
                
                # 显示引用信息
                if hasattr(response, 'reference'):
                    self.show_references(response.reference)
            else:
                # 非流式输出
                response = self.session.ask(question=question, stream=False)
                print(f"\n{UI_CONFIG['agent_prompt']}{response.content}")
                
                # 显示引用信息
                if hasattr(response, 'reference'):
                    self.show_references(response.reference)
                    
        except Exception as e:
            print(f"\n✗ 提问失败: {e}")
    
    def handle_command(self, user_input):
        """处理特殊命令"""
        user_input_lower = user_input.lower()
        
        # 退出命令
        if user_input_lower in UI_CONFIG["exit_commands"]:
            print("\n👋 再见！")
            return "exit"
        
        # 帮助命令
        elif user_input_lower in UI_CONFIG["help_commands"]:
            self.show_help()
            return "continue"
        
        # 清屏命令
        elif user_input_lower in UI_CONFIG["clear_commands"]:
            import os
            os.system('cls' if os.name == 'nt' else 'clear')
            return "continue"
        
        return "question"
    
    def show_help(self):
        """显示帮助信息"""
        help_text = f"""
📖 帮助信息:
  对话: 直接输入问题与Agent对话
  退出: {', '.join(UI_CONFIG['exit_commands'])}
  帮助: {', '.join(UI_CONFIG['help_commands'])}
  清屏: {', '.join(UI_CONFIG['clear_commands'])}
  强制退出: Ctrl+C

💡 提示:
  - 支持流式输出，实时显示回答
  - 会显示相关的参考资料和相似度
  - 输入内容不能为空
        """
        print(help_text)
    
    def start_chat(self):
        """开始对话循环"""
        self.show_welcome()
        
        while True:
            try:
                # 获取用户输入
                user_input = input(f"\n{UI_CONFIG['user_prompt']}").strip()
                
                # 检查空输入
                if not user_input:
                    print("请输入您的问题...")
                    continue
                
                # 处理命令
                command_result = self.handle_command(user_input)
                
                if command_result == "exit":
                    break
                elif command_result == "continue":
                    continue
                elif command_result == "question":
                    # 向Agent提问
                    self.ask_question(user_input)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"\n✗ 发生错误: {e}")
    
    def run(self):
        """运行对话程序"""
        # 连接服务
        if not self.connect():
            return False
        
        # 获取Agent
        if not self.get_agent():
            return False
        
        # 创建会话
        if not self.create_session():
            return False
        
        # 开始对话
        self.start_chat()
        return True

def main():
    """主函数"""
    try:
        # 创建并运行对话程序
        chat_client = RAGFlowAgentChat()
        success = chat_client.run()
        
        if not success:
            print("\n程序启动失败，请检查配置信息")
            sys.exit(1)
            
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
