Metadata-Version: 2.4
Name: ragflow-sdk
Version: 0.20.0
Summary: Python client sdk of [RAGFlow](https://github.com/infiniflow/ragflow). RAGFlow is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding.
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
License: Apache License, Version 2.0
Requires-Python: <3.13,>=3.10
Description-Content-Type: text/markdown
Requires-Dist: requests<3.0.0,>=2.30.0
Requires-Dist: beartype<0.19.0,>=0.18.5

# ragflow-sdk

# build and publish python SDK to pypi.org

```shell
uv build
uv pip install twine
export TWINE_USERNAME="__token__"
export TWINE_PASSWORD=$YOUR_PYPI_API_TOKEN
twine upload dist/*.whl
```
