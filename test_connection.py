#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow连接测试脚本
用于测试RAGFlow服务连接和Agent可用性
"""

from ragflow_sdk import RAGFlow

# 配置信息
API_KEY = "ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm"
BASE_URL = "http://117.72.181.138:180/"
AGENT_ID = "92a162f6710011f097860242ac130006"

def test_connection():
    """测试RAGFlow服务连接"""
    print("🔍 测试RAGFlow服务连接...")
    print(f"Base URL: {BASE_URL}")
    print(f"API Key: {API_KEY[:20]}...")
    print("-" * 50)
    
    try:
        # 1. 测试连接
        print("1. 正在连接RAGFlow服务...")
        rag_client = RAGFlow(api_key=API_KEY, base_url=BASE_URL)
        print("   ✓ 连接成功")
        
        # 2. 测试获取Agent列表
        print("\n2. 正在获取Agent列表...")
        agents = rag_client.list_agents()
        print(f"   ✓ 成功获取 {len(agents)} 个Agent")
        
        # 显示所有Agent信息
        if agents:
            print("\n   📋 Agent列表:")
            for i, agent in enumerate(agents, 1):
                agent_id = getattr(agent, 'id', 'Unknown')
                agent_title = getattr(agent, 'title', 'Unknown')
                print(f"      [{i}] ID: {agent_id}")
                print(f"          标题: {agent_title}")
        
        # 3. 测试指定的Agent
        print(f"\n3. 正在测试指定Agent (ID: {AGENT_ID})...")
        target_agents = rag_client.list_agents(id=AGENT_ID)
        
        if target_agents:
            agent = target_agents[0]
            agent_title = getattr(agent, 'title', 'Unknown')
            print(f"   ✓ 找到目标Agent: {agent_title}")
            
            # 4. 测试创建会话
            print("\n4. 正在测试创建会话...")
            session = agent.create_session()
            session_id = getattr(session, 'id', 'Unknown')
            print(f"   ✓ 会话创建成功 (ID: {session_id})")
            
            # 5. 测试简单对话
            print("\n5. 正在测试简单对话...")
            test_question = "你好"
            print(f"   问题: {test_question}")
            
            try:
                response = session.ask(question=test_question, stream=False)
                response_content = getattr(response, 'content', 'No content')
                print(f"   回答: {response_content[:100]}...")
                print("   ✓ 对话测试成功")
                
                # 显示引用信息
                if hasattr(response, 'reference') and response.reference:
                    print(f"   📚 引用资料数量: {len(response.reference)}")
                    for i, ref in enumerate(response.reference[:3], 1):  # 只显示前3个
                        doc_name = getattr(ref, 'document_name', 'Unknown')
                        similarity = getattr(ref, 'similarity', 0)
                        print(f"      [{i}] {doc_name} (相似度: {similarity:.3f})")
                
            except Exception as e:
                print(f"   ✗ 对话测试失败: {e}")
            
        else:
            print(f"   ✗ 未找到指定的Agent (ID: {AGENT_ID})")
            return False
        
        print("\n" + "="*50)
        print("🎉 所有测试通过！可以开始使用对话程序。")
        print("="*50)
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        print("\n💡 可能的解决方案:")
        print("   1. 检查网络连接")
        print("   2. 确认RAGFlow服务正在运行")
        print("   3. 验证API Key是否正确")
        print("   4. 确认Base URL是否正确")
        print("   5. 检查Agent ID是否存在")
        return False

def main():
    """主函数"""
    print("🚀 RAGFlow连接测试工具")
    print("="*50)
    
    success = test_connection()
    
    if success:
        print("\n✅ 测试完成，可以运行对话程序:")
        print("   python simple_agent_chat.py")
        print("   python ragflow_agent_chat.py")
        print("   python agent_chat_with_config.py")
    else:
        print("\n❌ 测试失败，请检查配置后重试")

if __name__ == "__main__":
    main()
