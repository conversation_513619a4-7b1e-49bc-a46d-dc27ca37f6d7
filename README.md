# RAGFlow Agent 对话程序

基于RAGFlow Python SDK开发的Agent对话程序，支持与RAGFlow Agent进行实时对话。

## 功能特性

- ✅ 支持与RAGFlow Agent实时对话
- ✅ 流式输出响应内容
- ✅ 显示引用资料信息
- ✅ 简洁的命令行界面
- ✅ 错误处理和异常捕获

## 安装依赖

```bash
pip install ragflow-sdk
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

## 配置信息

在使用前，请确保以下配置信息正确：

- **API Key**: `ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm`
- **Base URL**: `http://**************:180/`
- **Agent ID**: `92a162f6710011f097860242ac130006`

## 使用方法

### 方法1：运行完整版程序

```bash
python ragflow_agent_chat.py
```

完整版程序包含：
- 完整的错误处理
- 帮助命令
- 清屏功能
- 详细的引用信息显示

### 方法2：运行简化版程序

```bash
python simple_agent_chat.py
```

简化版程序更加轻量，适合快速测试。

## 程序界面

```
============================================================
🚀 RAGFlow Agent 对话程序
============================================================
输入 'quit', 'exit', 'q' 退出程序
输入 'help' 查看帮助信息
输入 'clear' 清屏
============================================================

👤 您: 你好，请介绍一下自己

🤖 Agent: 您好！我是基于RAGFlow的AI助手...

📚 参考资料:
  [1] 文档: example.pdf (相似度: 0.856)
      内容摘要: 这是一个关于AI助手的介绍文档...

👤 您: quit

👋 再见！
```

## 支持的命令

- `quit`, `exit`, `q` - 退出程序
- `help`, `h` - 显示帮助信息（完整版）
- `clear`, `cls` - 清屏（完整版）
- `Ctrl+C` - 强制退出

## 文件说明

- `ragflow_agent_chat.py` - 完整版对话程序
- `simple_agent_chat.py` - 简化版对话程序
- `config.py` - 配置文件
- `requirements.txt` - 依赖包列表
- `README.md` - 说明文档

## 注意事项

1. 确保RAGFlow服务正常运行且可访问
2. 确保API Key有效且有足够权限
3. 确保Agent ID存在且可用
4. 网络连接稳定

## 错误排查

### 连接失败
- 检查Base URL是否正确
- 检查网络连接
- 检查RAGFlow服务状态

### 认证失败
- 检查API Key是否正确
- 检查API Key是否过期

### Agent不存在
- 检查Agent ID是否正确
- 确认Agent是否已创建并发布

## API参考

程序基于RAGFlow Python SDK开发，主要使用以下API：

- `RAGFlow()` - 初始化客户端
- `list_agents()` - 获取Agent列表
- `create_session()` - 创建对话会话
- `ask()` - 发送问题并获取回答

更多API信息请参考：https://ragflow.com.cn/docs/dev/python_api_reference
