#!/bin/bash

# RAGFlow Agent 对话程序启动器

show_menu() {
    clear
    echo "========================================"
    echo "RAGFlow Agent 对话程序启动器"
    echo "========================================"
    echo
    echo "请选择要运行的程序:"
    echo "[1] 测试连接"
    echo "[2] 简化版对话程序"
    echo "[3] 完整版对话程序"
    echo "[4] 配置版对话程序"
    echo "[5] 安装依赖"
    echo "[0] 退出"
    echo
}

while true; do
    show_menu
    read -p "请输入选择 (0-5): " choice
    
    case $choice in
        1)
            echo
            echo "正在运行连接测试..."
            python3 test_connection.py
            read -p "按回车键继续..."
            ;;
        2)
            echo
            echo "正在启动简化版对话程序..."
            python3 simple_agent_chat.py
            read -p "按回车键继续..."
            ;;
        3)
            echo
            echo "正在启动完整版对话程序..."
            python3 ragflow_agent_chat.py
            read -p "按回车键继续..."
            ;;
        4)
            echo
            echo "正在启动配置版对话程序..."
            python3 agent_chat_with_config.py
            read -p "按回车键继续..."
            ;;
        5)
            echo
            echo "正在安装依赖..."
            pip3 install ragflow-sdk
            echo
            echo "依赖安装完成！"
            read -p "按回车键继续..."
            ;;
        0)
            echo "再见！"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            read -p "按回车键继续..."
            ;;
    esac
done
