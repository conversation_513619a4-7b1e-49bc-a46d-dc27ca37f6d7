#!/usr/bin/env python3
# --------------------( LICENSE                            )--------------------
# Copyright (c) 2014-2024 Beartype authors.
# See "LICENSE" for further details.

'''
Beartype **exception hierarchy** (i.e., public and private exception subclasses
raised at decoration, call, and usage time by :mod:`beartype`).

This private submodule is *not* intended for importation by downstream callers.
'''

# ....................{ IMPORTS                            }....................
#!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
# WARNING: To avoid polluting the public module namespace, external attributes
# should be locally imported at module scope *ONLY* under alternate private
# names (e.g., "from argparse import ArgumentParser as _ArgumentParser" rather
# than merely "from argparse import ArgumentParser").
#!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
from abc import ABCMeta as _ABCMeta

# ....................{ PRIVATE ~ mixins                   }....................
class _BeartypeHintForwardRefExceptionMixin(Exception, metaclass=_ABCMeta):
    '''
    Mixin of all **beartype forward reference exceptions** (i.e., exceptions
    concerning parent type hints containing one or more forward references to
    child type hints that have yet to be declared).

    This mixin enables internal logic throughout the :mod:`beartype` codebase to
    conveniently, efficiently, and transparently handle *all* forward reference
    exceptions -- including:

    * :exc:`.BeartypeCallHintForwardRefException`.
    * :exc:`.BeartypeDecorHintForwardRefException`.
    '''

    pass

# ....................{ SUPERCLASS                         }....................
class BeartypeException(Exception, metaclass=_ABCMeta):
    '''
    Abstract base class of all **beartype exceptions.**

    Instances of subclasses of this exception are raised either:

    * At decoration time from the :func:`beartype.beartype` decorator.
    * At call time from the new callable generated by the
      :func:`beartype.beartype` decorator to wrap the original callable.
    '''

    # ..................{ INITIALIZERS                       }..................
    def __init__(self, message: str) -> None:
        '''
        Initialize this exception.

        This constructor (in order):

        #. Passes all passed arguments as is to the superclass constructor.
        #. Sanitizes the fully-qualified module name of this
           exception from the private ``"beartype.roar._roarexc"`` submodule to
           the public ``"beartype.roar"`` subpackage to both improve the
           readability of exception messages and discourage end users from
           accessing this private submodule. By default, Python emits less
           readable and dangerous exception messages resembling:

               beartype.roar._roarexc.BeartypeCallHintParamViolation:
               @beartyped quote_wiggum_safer() parameter lines=[] violates type
               hint typing.Annotated[list[str], Is[lambda lst: bool(lst)]], as
               value [] violates validator Is[lambda lst: bool(lst)].
        '''
        assert isinstance(message, str), (
            f'{repr(message)} not exception message.')

        # If...
        #
        # Note that this logic unavoidably duplicates the body of the existing
        # beartype._util.text.utiltextmunge.uppercase_str_char_first() function
        # for safety. Attempting to call *ANY* beartype-specific callable
        # (including that function) from within an exception initializer would
        # invite a shocking calamity that would surely shatter the whole world.
        if (
            # This message contains at least two characters *AND*...
            len(message) >= 2 and
            # The first character of this message is lowercase...
            message[0].islower()
        ):
            # Then uppercase only this character for readability.
            message = f'{message[0].upper()}{message[1:]}'

        # Defer to the superclass constructor.
        super().__init__(message)

        # Sanitize the fully-qualified module name of the class of this
        # exception. See the docstring for justification.
        self.__class__.__module__ = 'beartype.roar'
        # print(f'{self.__class__.__name__}: {message}')

# ....................{ DECORATOR                          }....................
class BeartypeDecorException(BeartypeException):
    '''
    Abstract base class of all **beartype decorator exceptions.**

    Instances of subclasses of this exception are raised at decoration time
    from the :func:`beartype.beartype` decorator.
    '''

    pass

# ....................{ DECORATOR ~ wrapp[ee|er]           }....................
class BeartypeDecorWrappeeException(BeartypeDecorException):
    '''
    **Beartype decorator wrappee exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator when passed a **wrappee** (i.e., object
    to be decorated by this decorator) of invalid type.
    '''

    pass


class BeartypeDecorWrapperException(BeartypeDecorException):
    '''
    **Beartype decorator parse exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on accidentally generating an **invalid
    wrapper** (i.e., syntactically invalid new callable to wrap the original
    callable).
    '''

    pass

# ....................{ DECORATOR ~ hint                   }....................
class BeartypeDecorHintException(BeartypeDecorException):
    '''
    Abstract base class of all **beartype decorator type hint exceptions.**

    Instances of subclasses of this exception are raised at decoration time
    from the :func:`beartype.beartype` decorator on receiving a callable
    annotated by one or more **invalid type hints** (i.e., annotations that are
    neither PEP-compliant nor PEP-compliant type hints supported by this
    decorator).
    '''

    pass


class BeartypeDecorHintForwardRefException(
    BeartypeDecorHintException, _BeartypeHintForwardRefExceptionMixin):
    '''
    **Beartype decorator forward reference type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated by an
    **invalid forward reference type hint** (i.e., string whose value is the
    name of a user-defined class that has yet to be declared).
    '''

    pass


class BeartypeDecorHintTypeException(BeartypeDecorHintException):
    '''
    **Beartype decorator class type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated by an
    **invalid class type hint** (i.e., class invalid for use as a type hint,
    typically due to failing to support runtime :func:`isinstance` calls).
    '''

    pass

# ....................{ DECORATOR ~ hint : non-pep         }....................
class BeartypeDecorHintNonpepException(BeartypeDecorHintException):
    '''
    **Beartype decorator PEP-noncompliant type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated by an
    **invalid PEP-noncompliant type hint** (i.e., type hint failing to comply
    with :mod:`beartype`-specific semantics, including tuple unions and
    fully-qualified forward references).

    Tuple unions, for example, are required to contain *only* PEP-noncompliant
    annotations. This exception is thus raised for callables type-hinted with
    tuples containing one or more PEP-compliant items (e.g., instances or
    classes declared by the stdlib :mod:`typing` module) *or* arbitrary objects
    (e.g., dictionaries, lists, numbers, sets).
    '''

    pass


class BeartypeDecorHintNonpepNumpyException(BeartypeDecorHintNonpepException):
    '''
    **Beartype decorator PEP-noncompliant NumPy type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated by an
    **invalid NumPy type hint** (e.g., ``numpy.typing.NDArray[...]`` type hint
    subscripted by an invalid number of arguments).
    '''

    pass


class BeartypeDecorHintNonpepPanderaException(BeartypeDecorHintNonpepException):
    '''
    **Beartype decorator PEP-noncompliant Pandera type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated by an
    **invalid Pandera type hint** (e.g., ``pandera.typing.DataFrame[...]`` type
    hint annotating a parameter or return of a callable *not* decorated by the
    PEP-noncompliant :func:`pandera.check_types` decorator).
    '''

    pass

# ....................{ DECORATOR ~ hint : pep             }....................
class BeartypeDecorHintPepException(BeartypeDecorHintException):
    '''
    Abstract base class of all **beartype decorator PEP-compliant type hint
    value exceptions.**

    Instances of subclasses of this exception are raised at decoration time
    from the :func:`beartype.beartype` decorator on receiving a callable
    annotated with one or more PEP-compliant type hints either violating an
    annotation-centric PEP (e.g., :pep:`484`) *or* this decorator's
    implementation of such a PEP.
    '''

    pass


class BeartypeDecorHintPepSignException(BeartypeDecorHintPepException):
    '''
    **Beartype decorator PEP-compliant type hint sign exception.**

    Instances of subclasses of this exception are raised at decoration time
    from the :func:`beartype.beartype` decorator on receiving a callable
    annotated with one or more PEP-compliant type hints *not* uniquely
    identifiable by a **sign** (i.e., object uniquely identifying a category
    of PEP-compliant type hints).
    '''

    pass


class BeartypeDecorHintPepUnsupportedException(BeartypeDecorHintPepException):
    '''
    **Beartype decorator unsupported PEP-compliant type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints (e.g., instances or classes declared
    by the stdlib :mod:`typing` module) currently unsupported by this
    decorator.
    '''

    pass

# ....................{ DECORATOR ~ hint : pep : proposal  }....................
class BeartypeDecorHintPep3119Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`3119`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`3119` *or* this
    decorator's implementation of :pep:`3119`, including:

    * Hints that are **non-isinstanceable classes** (i.e., classes that
      prohibit being passed as the second parameter to the :func:`isinstance`
      builtin by leveraging metaclasses overriding the ``__instancecheck__()``
      dunder method to raise exceptions). Notably, this includes most public
      classes declared by the standard :mod:`typing` module.
    '''

    pass


class BeartypeDecorHintPep484Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`484`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`484` *or* this
    decorator's implementation of :pep:`484`, including:

    * Hints subscripted by the :attr:`typing.NoReturn` type hint (e.g.,
      ``typing.List[typing.NoReturn]``).
    '''

    pass


class BeartypeDecorHintPep484585Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`484`- or :pep:`585`-compliant **dual type hint
    exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints violating :pep:`484`, :pep:`585`, *or*
    this decorator's implementation of :pep:`484` or :pep:`585`.
    '''

    pass


class BeartypeDecorHintPep544Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`544`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`544` *or* this
    decorator's implementation of :pep:`544`.
    '''

    pass


class BeartypeDecorHintPep557Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`557`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`557` *or* this
    decorator's implementation of :pep:`557`.
    '''

    pass


class BeartypeDecorHintPep585Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`585`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`585` *or* this
    decorator's implementation of :pep:`585`.
    '''

    pass


class BeartypeDecorHintPep586Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`586`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`586` *or* this
    decorator's implementation of :pep:`586`.
    '''

    pass


class BeartypeDecorHintPep591Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`591`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`591` *or* this
    decorator's implementation of :pep:`591`.
    '''

    pass


class BeartypeDecorHintPep593Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`593`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`593` *or* this
    decorator's implementation of :pep:`593`.
    '''

    pass


class BeartypeDecorHintPep604Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`604`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`604` *or* this
    decorator's implementation of :pep:`604`.
    '''

    pass

class BeartypeDecorHintPep647Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`647`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`647` *or* this
    decorator's implementation of :pep:`647`.
    '''

    pass


class BeartypeDecorHintPep673Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`673`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`673` *or* this
    decorator's implementation of :pep:`673`.
    '''

    pass


class BeartypeDecorHintPep695Exception(BeartypeDecorHintPepException):
    '''
    **Beartype decorator** :pep:`695`-compliant **type hint exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable annotated with
    one or more PEP-compliant type hints either violating :pep:`695` *or* this
    decorator's implementation of :pep:`695`.
    '''

    pass

# ....................{ DECORATOR ~ param                  }....................
class BeartypeDecorParamException(BeartypeDecorException):
    '''
    Abstract base class of all **beartype decorator parameter exceptions.**

    Instances of subclasses of this exception are raised at decoration time
    from the :func:`beartype.beartype` decorator on receiving a callable
    declaring invalid parameters.
    '''

    pass


class BeartypeDecorParamNameException(BeartypeDecorParamException):
    '''
    **Beartype decorator parameter name exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator on receiving a callable declaring
    parameters with **invalid names** (i.e., prefixed by the
    :mod:`beartype`-reserved substring ``"__bear"``).
    '''

    pass

# ....................{ CALL                               }....................
class BeartypeCallException(BeartypeException):
    '''
    Abstract base class of all **beartyped callable exceptions.**

    Instances of subclasses of this exception are raised from wrapper functions
    generated by the :func:`beartype.beartype` decorator, typically when
    failing a runtime type-check at call time.
    '''

    pass


class BeartypeCallUnavailableTypeException(BeartypeCallException):
    '''
    **Beartyped callable unavailable type exceptions.**

    This exception is raised from the :class:`beartype.cave.UnavailableType`
    class when passed to either the :func:`isinstance` or :func:`issubclass`
    builtin functions, typically due to a type defined by the
    :class:`beartype.cave` submodule being conditionally unavailable under the
    active Python interpreter.
    '''

    pass

# ....................{ CALL ~ hint                        }....................
class BeartypeCallHintException(BeartypeCallException):
    '''
    Abstract base class of all **beartype type-checking exceptions.**

    Instances of subclasses of this exception are raised from wrapper functions
    generated by the :func:`beartype.beartype` decorator when failing a runtime
    type-check at callable call time, typically due to either being passed a
    parameter or returning a value violating a type hint annotating that
    parameter or return.
    '''

    pass


class BeartypeCallHintForwardRefException(
    BeartypeCallHintException, _BeartypeHintForwardRefExceptionMixin):
    '''
    **Beartype type-checking forward reference exception.**

    This exception is raised from wrapper functions generated by the
    :func:`beartype.beartype` decorator when a **forward reference type hint**
    (i.e., string whose value is the name of a user-defined class that has yet
    to be defined) erroneously references a module attribute whose value is
    *not* actually a class.
    '''

    pass

# ....................{ CALL ~ hint : violation            }....................
class BeartypeCallHintViolation(BeartypeCallHintException):
    '''
    Abstract base class of all **beartype type-checking violations.**

    Instances of subclasses of this exception are raised by :mod:`beartype` when
    an object to be type-checked violates the type hint annotating that object.
    This includes wrapper functions generated by the :func:`beartype.beartype`
    decorator when either passed a parameter or returning an object violating
    the type hint annotating that parameter or return.

    Attributes
    ----------
    _culprits_weakref_and_repr : Tuple[(object, str), ...]
        Tuple of 2-tuples (``culprit_weakref``, ``culprit_repr``) weakly
        referring to all of the culprits previously passed to the
        :meth:`__init__` method, where:

        * ``culprits_repr`` is the machine-readable string representation of the
          culprit weakly referred to by the ``culprit_weakref`` reference.
        * ``culprits_weakref`` is a weak reference to that culprit, defined as
            either:

            * If that culprit is not ``None`` *and* that culprit can be weakly
              referenced, a **weak reference** (i.e., :class:`weakref.ref`
              instance) to that culprit.
            * If that culprit is ``None``, a singleton non-``None`` placeholder.
              Since the :class:`weakref.ref` class ambiguously returns ``None``
              when that culprit has already been garbage-collected, this
              attribute intentionally substitutes ``None`` for this placeholder.
            * If that culprit *cannot* be weakly referenced (e.g., due to being
              an instance of a builtin variable-sized C-based type), ``None``.
    '''

    # ..................{ INITIALIZERS                       }..................
    def __init__(self, message: str, culprits: tuple) -> None:
        '''
        Initialize this type-checking exception.

        Parameters
        ----------
        message : str
            Human-readable message describing this exception.
        culprits : Tuple[object, ...]
            Tuple of one or more **culprits** (i.e., user-defined objects
            directly responsible for this exception, typically due to violating
            a type hint annotating a parameter passed to *or* object returned
            from the wrapper function generated by the :func:`beartype.beartype`
            decorator raising this exception). This exception internally
            preserves a weak reference to these culprits, which callers may then
            safely retrieve at any time via the :meth:`culprits` property.

        Raises
        ------
        _BeartypeUtilExceptionException
            If the culprits are either:

            * *Not* a tuple.
            * The empty tuple.
        '''

        # Avoid circular import dependencies.
        from beartype._util.py.utilpyweakref import make_obj_weakref_and_repr

        # Initialize the superclass with the passed message.
        super().__init__(message)

        #FIXME: Unit test us up, please.
        # If the culprits are *NOT* a tuple, raise an exception.
        if not isinstance(culprits, tuple):
            raise _BeartypeUtilExceptionException(
                f'Culprits {repr(culprits)} not tuple.')
        # Else, the culprits are a tuple.
        #
        # If the culprits are the empty tuple, raise an exception.
        elif not culprits:
            raise _BeartypeUtilExceptionException('Culprits tuple empty.')
        # Else, the culprits are a non-empty tuple.

        # Tuple of 2-tuples ("culprit_weakref", "culprit_repr") weakly referring
        # to all of the passed culprits.
        self._culprits_weakref_and_repr = tuple(
            make_obj_weakref_and_repr(culprit)
            for culprit in culprits
        )

    # ..................{ PROPERTIES                         }..................
    # Read-only properties intentionally providing no corresponding setters.

    @property
    def culprits(self) -> tuple:
        '''
        Tuple of one or more **culprits** (i.e., user-defined objects directly
        responsible for this exception, typically due to violating a type hint
        annotating a parameter passed to *or* object returned from the wrapper
        function generated by the :func:`beartype.beartype` decorator raising
        this exception).

        Specifically, this property returns either:

        * If a container (e.g., dictionary, list, set, tuple) is responsible for
          this exception, the 2-tuple ``(culprit_root, culprit_leaf)`` where:

          * ``culprit_root`` is the outermost such container. Typically, this is
            the passed parameter or returned value indirectly violating this
            hint.
          * ``culprit_leaf`` is the innermost item transitively contained in
            ``culprit_root`` directly violating this hint.

        * If a non-container (e.g., scalar, class instance) is responsible for
          this exception, the 1-tuple ``(culprit,)`` where ``culprit`` is that
          non-container.

        Caveats
        -------
        **This property is safely accessible from any context.** However, this
        property is most usefully accessed *only* from the ``except ...:`` block
        directly catching this exception. To avoid memory leaks, this property
        only weakly rather than strongly refers to these culprits and is thus
        best accessed only where these culprits are accessible. Notably, this
        property is guaranteed to refer to these culprits *only* for the
        duration of the ``except ...:`` block directly catching this exception.
        Since these culprits may be garbage-collected at any time thereafter,
        this property *cannot* be guaranteed to refer to these culprits outside
        that block. If this property is accessed from *any* other context and
        ore or more of these culprits have already been garbage-collected, the
        corresponding item(s) of this property are only the machine-readable
        representations of those culprits rather than those actual culprits.

        **This property returns the machine-readable representation of instances
        of builtin variable-sized C-based types** (e.g., :class:`dict`,
        :class:`int`, :class:`list`, :class:`tuple`) **rather than those
        instances themselves.** Why? Because CPython limitations prevent those
        instances from being weakly referred to. Blame Guido and the BDFL!
        '''

        # Avoid circular import dependencies.
        from beartype._util.py.utilpyweakref import get_weakref_obj_or_repr

        # Tuple of one or more strong references to the culprits previously
        # passed to the __init__() method for those culprits that are alive
        # *OR* their representations otherwise.
        culprits = tuple(
            get_weakref_obj_or_repr(
                obj_weakref=culprit_weakref, obj_repr=culprit_repr)
            for culprit_weakref, culprit_repr in self._culprits_weakref_and_repr
        )
        # print(f'culprits_weakref_and_repr: {self._culprits_weakref_and_repr}')

        # Return these culprits.
        return culprits


class BeartypeCallHintParamViolation(BeartypeCallHintViolation):
    '''
    **Beartyped callable parameter type-checking exception.**

    This exception is raised from a call to a wrapper function generated by the
    :func:`beartype.beartype` decorator type-checking a decorated callable when
    the caller passes that call a parameter violating the type hint annotating
    that parameter of that decorated callable.
    '''

    pass


class BeartypeCallHintReturnViolation(BeartypeCallHintViolation):
    '''
    **Beartyped callable return type-checking exception.**

    This exception is raised from a call to a wrapper function generated by the
    :func:`beartype.beartype` decorator type-checking a decorated callable when
    that call returns an object violating the type hint annotating the return
    of that decorated callable.
    '''

    pass


class BeartypeDecorHintParamDefaultViolation(BeartypeCallHintViolation):
    '''
    **Beartyped decorator optional parameter default value type-checking
    exception.**

    This exception is raised at decoration time by the :func:`beartype.beartype`
    decorator when type-checking a decorated callable accepting an optional
    parameter whose default value violates the type hint annotating that
    parameter.
    '''

# ....................{ PEP                                }....................
class BeartypePepException(BeartypeDecorException):
    '''
    Abstract base class of all **beartype Python Enhancement Proposal (PEP)
    exceptions.**

    Instances of subclasses of this exception are raised at both call time and
    decoration time on receiving a callable or class violating a specific PEP.
    '''

    pass


class BeartypePep563Exception(BeartypePepException):
    '''
    **Beartype** :pep:`563` **exception.**

    This exception is raised at both call time of the
    :func:`beartype.peps.resolve_pep563` function and decoration time of the
    :func:`beartype.beartype` decorator on failing to dynamically evaluate a
    postponed annotation of a callable for which :pep:`563` is active.
    '''

    pass

# ....................{ API ~ cave                         }....................
class BeartypeCaveException(BeartypeException):
    '''
    Abstract base class of all **beartype cave exceptions.**

    Instances of subclasses of this exception are raised at usage time from
    various types published by the :func:`beartype.cave` submodule.
    '''

    pass

# ....................{ API ~ cave : nonetypeor            }....................
class BeartypeCaveNoneTypeOrException(BeartypeCaveException):
    '''
    Abstract base class of all **beartype cave** ``None`` **tuple factory
    exceptions.**

    Instances of subclasses of this exception are raised at usage time from
    the :func:`beartype.cave.NoneTypeOr` tuple factory.
    '''

    pass


class BeartypeCaveNoneTypeOrKeyException(BeartypeCaveNoneTypeOrException):
    '''
    **Beartype cave** ``None`` **tuple factory key exception.**

    Instances of this exception are raised when indexing the :func:
    `beartype.cave.NoneTypeOr` dictionary with an invalid key, including:

    * The empty tuple.
    * Arbitrary objects that are neither:

      * **Types** (i.e., :class:`beartype.cave.ClassType` instances).
      * **Tuples of types** (i.e., tuples whose items are all
        :class:`beartype.cave.ClassType` instances).
    '''

    pass


class BeartypeCaveNoneTypeOrMutabilityException(
    BeartypeCaveNoneTypeOrException):
    '''
    **Beartype cave** ``None`` **tuple factory mutability exception.**

    Instances of this exception are raised when attempting to explicitly set a
    key on the :func:`beartype.cave.NoneTypeOr` dictionary.
    '''

    pass

# ....................{ API ~ claw                         }....................
class BeartypeClawException(BeartypeException):
    '''
    Abstract base class of all **beartype import hook exceptions.**

    Instances of subclasses of this exception are raised at call time from the
    callables and classes published by the :mod:`beartype.claw` subpackage.
    '''

    pass

# ....................{ API ~ claw : hook                  }....................
class BeartypeClawHookException(BeartypeClawException):
    '''
    **Beartype import hook-time exception.**

    This exception is raised at **beartype import hook-time** (i.e., the early
    time encompassing the call to a public beartype import hook published by the
    :mod:`beartype.claw` subpackage by a downstream third-party codebase) on
    various fatal errors (e.g., when that codebase calls that hook with invalid
    parameters).
    '''

    pass


class BeartypeClawHookUnpackagedException(BeartypeClawHookException):
    '''
    **Beartype import hook-time unpackaged exception.**

    This exception is raised at **beartype import hook-time** (i.e., the early
    time encompassing the call to a public beartype import hook published by the
    :mod:`beartype.claw` subpackage by a downstream third-party codebase) when
    the :func:`beartype.claw.beartype_this_package` function is called from
    outside any package structure (e.g., top-level module or executable script).
    '''

    pass

# ....................{ API ~ claw : import                }....................
class BeartypeClawImportException(BeartypeClawException):
    '''
    **Beartype import hook import exception.**

    This exception is raised at import time when importing a module erroneously
    transformed by a beartype import hook previously installed by a prior call
    to a public function published by the :mod:`beartype.claw` subpackage.
    '''

    pass


class BeartypeClawImportAstException(BeartypeClawImportException):
    '''
    **Beartype import hook abstract syntax tree (AST) exception.**

    This exception is raised at import time when a **beartype import hook**
    (i.e., previously installed by a prior call to a public function published
    by the :mod:`beartype.claw` subpackage) erroneously transforms a module from
    its original syntactically valid AST into a new syntactically invalid AST.
    '''

    pass


class BeartypeClawImportConfException(BeartypeClawImportException):
    '''
    **Beartype import hook configuration exception.**

    This exception is raised at import time when a **beartype import hook**
    (i.e., previously installed by a prior call to a public function published
    by the :mod:`beartype.claw` subpackage) erroneously attempts to access a
    non-existent beartype configuration.
    '''

    pass

# ....................{ API ~ conf                         }....................
class BeartypeConfException(BeartypeException):
    '''
    Abstract base class of all **beartype configuration exceptions.**

    Instances of subclasses of this exception are raised by the
    :class:`beartype.BeartypeConf` class to inform the user of various fatal
    edge cases concerning beartype configuration.
    '''

    pass


class BeartypeConfParamException(BeartypeConfException):
    '''
    **Beartype configuration parameter exception.**

    Instances of this exception are raised at instantiation time of the
    :class:`beartype.BeartypeConf` class when the caller attempts to erroneously
    instantiate that class with an invalid parameter.
    '''

    pass


class BeartypeConfShellVarException(BeartypeConfException):
    '''
    **Beartype configuration shell environment variable exception.**

    Instances of this exception are raised at instantiation time of the
    :class:`beartype.BeartypeConf` class when the caller erroneously sets a
    shell environment variable recognized by that class (e.g.,
    ``${BEARTYPE_IS_COLOR}``) to an invalid value.
    '''

    pass

# ....................{ API ~ door                         }....................
class BeartypeDoorException(BeartypeException):
    '''
    Abstract base class of all **Decidedly Object-Oriented Runtime-checking
    (DOOR) exceptions.**

    Instances of subclasses of this exception are raised at call time from
    callables and classes published by the :func:`beartype.door` subpackage.
    '''

    pass


class BeartypeDoorHintViolation(BeartypeCallHintViolation):
    '''
    **Beartype object-oriented type-checking exception.**

    This exception is raised at call time by both:

    * The :func:`beartype.door.die_if_unbearable` function when passed an
      object violating the passed type hint.
    * The :meth:`beartype.door.TypeHint.die_if_unbearable` method when passed an
      object violating the current type hint.
    '''

    pass

# ....................{ API ~ door : pep                   }....................
class BeartypeDoorNonpepException(BeartypeDoorException):
    '''
    **Decidedly Object-Oriented Runtime-checking (DOOR) PEP-noncompliant type
    hint exception.**

    This exception is raised at call time from :func:`beartype.door` callables
    and classes on receiving an **invalid PEP-noncompliant type hint** (i.e.,
    type hint failing to comply with PEP standards currently supported by the
    :mod:`beartype.door` API).
    '''

    pass


class BeartypeDoorPepException(BeartypeDoorException):
    '''
    **Decidedly Object-Oriented Runtime-checking (DOOR) PEP-compliant type hint
    exception.**

    This exception is raised at call time from :func:`beartype.door` callables
    and classes on receiving an **invalid PEP-compliant type hint** (i.e.,
    type hint complying with PEP standards currently supported by the
    :mod:`beartype.door` API but otherwise invalid for various reasons).
    '''

    pass


class BeartypeDoorPepUnsupportedException(BeartypeDoorPepException):
    '''
    **Decidedly Object-Oriented Runtime-checking (DOOR) unsupported
    PEP-compliant type hint exception.**

    This exception is raised at call time from :func:`beartype.door` callables
    and classes on receiving an **unsupported PEP-compliant type hint** (i.e.,
    type hint complying with PEP standards *not* currently supported by the
    :mod:`beartype.door` API).
    '''

    pass

# ....................{ API ~ kind                         }....................
class BeartypeKindException(BeartypeException):
    '''
    Abstract base class of all **beartype container exceptions.**

    Instances of subclasses of this exception are raised at usage (e.g.,
    instantiation, callable call) time from various class hierarchies
    implementing **beartype containers** (i.e., pure-Python data structures
    defined by :mod:`beartype`).
    '''

    pass

# ....................{ API ~ kind : dict                  }....................
class BeartypeKindFrozenDictException(BeartypeException):
    '''
    **Beartype frozen dictionary exception.**

    This exception is raised from various methods of the private
    :class:`beartype._util.kind.map.utilmapfrozen.FrozenDict` class publicly
    exposed as the :class:`beartype.BeartypeHintOverrides` subclass, typically
    due to external callers erroneously attempting to modify key-value pairs of
    instances of this class.
    '''

    pass


class BeartypeHintOverridesException(BeartypeKindFrozenDictException):
    '''
    **Beartype hint overrides exception.**

    This exception is raised from various methods of the public
    :class:`beartype.BeartypeHintOverrides` class, typically due to external
    callers erroneously attempting to instantiate instances of this class with
    **recursive hint overrides** (e.g.,
    ``BeartypeHintOverrides{str: list[str]})``).
    '''

    pass

# ....................{ API ~ plug                         }....................
class BeartypePlugException(BeartypeException):
    '''
    Abstract base class of all **beartype plugin exceptions.**

    Instances of subclasses of this exception are raised at various times from
    functionality utilizing :mod:`beartype`-specific plugin APIs standardized by
    the :mod:`beartype.plug` subpackage, typically on detecting invalid usage of
    a :mod:`beartype`-specific plugin API within a third-party Python package or
    module.
    '''

    pass


class BeartypePlugInstancecheckStrException(BeartypePlugException):
    '''
    **Beartype** ``__instancecheck_str__()`` **exception.**

    This exception is raised at various times from functionality utilizing the
    :mod:`beartype`-specific ``__instancecheck_str__()`` plugin API (i.e., a
    :mod:`beartype`-specific dunder method defined on metaclasses of classes to
    return human-readable substrings describing the failure of arbitrary objects
    to satisfy those classes).
    '''

    pass

# ....................{ API ~ vale                         }....................
class BeartypeValeException(BeartypeException):
    '''
    Abstract base class of all **beartype validator exceptions.**

    Instances of subclasses of this exception are raised at usage (e.g.,
    instantiation, callable call) time from the class hierarchy published by
    the :mod:`beartype.vale` subpackage.
    '''

    pass


class BeartypeValeSubscriptionException(BeartypeValeException):
    '''
    **Beartype validator subscription exception.**

    This exception is raised at instantiation time when subscripting (indexing)
    factories published by the :mod:`beartype.vale` subpackage, including
    attempts to:

    * Instantiate *any* of these factories. Like standard type hints, these
      factories are *only* intended to be subscripted (indexed).
    * Apply the ``&`` or ``|`` operators to *any* subscriptions of these
      factories and *any* other objects (e.g.,
      ``beartype.vale.Is[lambda obj: True]] & 'If it seems bad, it is.'``).
    * Subscript the :attr:`beartype.vale.Is` factory by anything other than a
      **validator** (i.e., tester function satisfying the type hint
      ``collections.abc.Callable[[typing.Any,], bool]``).
    '''

    pass


class BeartypeValeValidationException(BeartypeValeException):
    '''
    **Beartype validator validation exception.**

    This exception is raised at validation time (e.g.,, at call time of a
    :func:`beartype.beartype`-decorated callable annotated by a beartype
    validator) when a beartype validator fails to properly validate an object,
    including attempts to:

    * Subscript the :attr:`beartype.vale.Is` factory by a **non-bool-like
      validator** (i.e., tester function returning an object that is neither a
      :class:`bool` *nor* implicitly convertible into a :class:`bool`).
    '''

    pass

# ....................{ PRIVATE ~ door                       }..................
class _BeartypeDoorTextException(BeartypeDoorException):
    '''
    **Decidedly Object-Oriented Runtime-checking (DOOR) text exception.**

    This exception is raised at call time from :func:`beartype.door` callables
    and classes on detecting invalid strings (e.g., on raising an exception
    whose message is *not* prefixed by the expected substring).
    '''

    pass

# ....................{ PRIVATE ~ vale                     }....................
class _BeartypeValeUtilException(BeartypeValeException):
    '''
    **Beartype validator utility exception.**

    This exception is raised from various submodules of the private
    :func:`beartype.vale._util` subpackage.
    '''

    pass

# ....................{ PRIVATE ~ util                     }....................
class _BeartypeUtilException(BeartypeException):
    '''
    Abstract base class of all **beartype private utility exceptions.**

    Instances of subclasses of this exception are raised by *most* (but *not*
    all) private submodules of the private :mod:`beartype._util` subpackage.
    These exceptions denote critical internal issues and should thus *never* be
    raised, let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilExceptionException(_BeartypeUtilException):
    '''
    **Beartype exception utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype.roar._roarexc` subpackage.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : ast                 }..................
class _BeartypeUtilAstException(_BeartypeUtilException):
    '''
    **Beartype abstract syntax tree (AST) utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.ast` subpackage.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : cache               }..................
class _BeartypeUtilCachedException(_BeartypeUtilException):
    '''
    Abstract base class of all **beartype caching utility exceptions.**

    Instances of subclasses of this exception are raised by private submodules
    of the private :mod:`beartype._util.cache` subpackage. These exceptions
    denote critical internal issues and should thus *never* be raised -- let
    alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilCallableCachedException(_BeartypeUtilCachedException):
    '''
    **Beartype memoization exception.**

    This exception is raised by the
    :func:`beartype._util.cache.utilcache.utilcachecall.callable_cached`
    decorator on various fatal errors (e.g., when the signature of the
    decorated callable is unsupported).

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilCacheLruException(_BeartypeUtilCachedException):
    '''
    **Beartype Least Recently Used (LRU) cache exception.**

    This exception is raised by the
    :func:`beartype._util.cache.utilcache.utilcachelru.CacheLruStrong` class
    on various fatal errors (e.g., when the cache capacity is *not* a positive
    integer).

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : cache : pool        }..................
class _BeartypeUtilCachedKeyPoolException(_BeartypeUtilException):
    '''
    **Beartype key pool exception.**

    This exception is raised by private functions of the private
    :mod:`beartype._util.cache.pool.utilcachepool` subpackage on various fatal
    edge cases.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''
    pass


class _BeartypeUtilCachedFixedListException(_BeartypeUtilCachedException):
    '''
    **Beartype decorator fixed list exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator when an internal callable erroneously
    mutates a **fixed list** (i.e., list constrained to a fixed length defined
    at instantiation time), usually by attempting to modify the length of that
    list.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilCachedObjectTypedException(_BeartypeUtilCachedException):
    '''
    **Beartype decorator typed object exception.**

    This exception is raised at decoration time from the
    :func:`beartype.beartype` decorator when an internal callable erroneously
    acquires a **pooled typed object** (i.e., object internally cached to a
    pool of all objects of that type).

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : call                }..................
class _BeartypeCallHintRaiseException(_BeartypeUtilException):
    '''
    Abstract base class of all **beartype human-readable exception raiser
    exceptions.**

    Instances of subclasses of this exception are raised by private utility
    **exception raiser functions** (i.e., functions raising human-readable
    exceptions from wrapper functions when either passed a parameter or
    returning a value annotated by a type hint fails the runtime type-check
    required by that hint) when an unexpected failure occurs.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeCallHintPepRaiseException(_BeartypeCallHintRaiseException):
    '''
    **Beartype PEP-compliant human-readable exception raiser exception.**

    This exception is raised by the
    :func:`beartype._check.error.errorget.get_func_pith_violation`
    exception raiser function when an unexpected failure occurs.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeCallHintPepRaiseDesynchronizationException(
    _BeartypeCallHintPepRaiseException):
    '''
    **Beartype human-readable exception raiser desynchronization exception.**

    This exception is raised by the
    :func:`beartype._check.error.errorget.get_func_pith_violation` function
    (which raises human-readable exceptions from wrapper functions when either
    passed a parameter or returning a value, referred to as the "pith" for
    brevity, annotated by a PEP-compliant type hint fails the type-check
    required by that hint) when this pith appears to satisfy this type-check, a
    runtime paradox implying either:

    * The parent wrapper function generated by the :mod:`beartype.beartype`
      decorator type-checking this pith triggered a false negative by
      erroneously misdetecting this pith as failing this type check.
    * The
        :func:`beartype._check.error.errorget.get_func_pith_violation`
      function re-type-checking this pith triggered a false positive by
      erroneously misdetecting this pith as satisfying this type check when in
      fact this pith fails to do so.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : kind                }..................
class _BeartypeUtilCallFrameException(_BeartypeUtilException):
    '''
    **Beartype call stack frame utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.utilfunc` subpackage. This exception denotes a critical
    internal issue and should thus *never* be raised -- let alone allowed to
    percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilMappingException(_BeartypeUtilException):
    '''
    **Beartype mapping utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.kind.map` subpackage. This exception denotes a
    critical internal issue and should thus *never* be raised -- let alone
    allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilModuleException(_BeartypeUtilException):
    '''
    **Beartype module utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.module.utilmodget` subpackage. Notably, this includes:

    * When dynamically importing an unimportable external user-defined module,
      typically due to a **PEP-compliant forward reference type hint** (i.e.,
      string whose value is the name of a user-defined class that has yet to be
      defined) erroneously referencing a non-existent module or module
      attribute.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilPathException(_BeartypeUtilException):
    '''
    **Beartype path utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.path` subpackage on various fatal edge cases. This
    exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilTypeException(_BeartypeUtilException):
    '''
    **Beartype class utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.cls` subpackage. This exception denotes a critical
    internal issue and should thus *never* be raised -- let alone allowed to
    percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : kind : callable     }..................
class _BeartypeUtilCallableException(_BeartypeUtilException):
    '''
    **Beartype callable utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.func` subpackage. This exception denotes a critical
    internal issue and should thus *never* be raised -- let alone allowed to
    percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilCallableScopeException(_BeartypeUtilCallableException):
    '''
    **Beartype callable scope utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.func.utilfuncscope` submodule. This exception denotes a
    critical internal issue and should thus *never* be raised -- let alone
    allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilCallableScopeNotFoundException(
    _BeartypeUtilCallableException):
    '''
    **Beartype callable missing scope utility exception.**

    This exception is raised by the private
    :mod:`beartype._util.func.utilfuncscope.get_func_locals` getter on failing
    to find the lexical scope of the parent callable or class declaring the
    passed nested callable, enabling callers of that getter to identify this
    common edge case. This exception denotes a critical internal issue and
    should thus *never* be raised -- let alone allowed to percolate up the call
    stack to end users.
    '''

    pass


class _BeartypeUtilCallableWrapperException(_BeartypeUtilCallableException):
    '''
    **Beartype callable wrapper utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.func.utilfuncwrap` subpackage. This exception denotes a
    critical internal issue and should thus *never* be raised -- let alone
    allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : object              }..................
class _BeartypeUtilObjectException(_BeartypeUtilException):
    '''
    Abstract base class of all **beartype object utility exceptions.**

    Instances of subclasses of this exception are raised by private functions
    defined by the private :mod:`beartype._util.utilobject` submodule. These
    exceptions denote critical internal issues and should thus *never* be raised
    -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilObjectNameException(_BeartypeUtilObjectException):
    '''
    **Beartype object name exception.**

    This exception is raised by the
    :func:`beartype._util.utilobject.get_object_basename_scoped` getter when
    the passed object is **unnamed** (i.e., fails to declare either the
    ``__name__`` or ``__qualname__`` dunder attributes). This exception denotes
    a critical internal issue and should thus *never* be raised -- let alone
    allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : python              }..................
class _BeartypeUtilPythonException(_BeartypeUtilException):
    '''
    Abstract base class of all beartype **Python utility exceptions.**

    Instances of subclasses of this exception are raised by private submodules
    of the private :mod:`beartype._util.py` subpackage. These exceptions
    denote critical internal issues and should thus *never* be raised -- let
    alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilPythonInterpreterException(_BeartypeUtilPythonException):
    '''
    Beartype **Python interpreter utility exception.**

    This exception is raised by private functions of the private
    :mod:`beartype._util.py.utilpyinterpreter` submodule on fatal edge cases.
    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilPythonWeakrefException(_BeartypeUtilPythonException):
    '''
    Beartype **Python weak reference utility exception.**

    This exception is raised by private functions of the private
    :mod:`beartype._util.py.utilpyweakref` submodule on fatal edge cases. This
    exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass

# ....................{ PRIVATE ~ util : text                }..................
class _BeartypeUtilTextException(_BeartypeUtilException):
    '''
    Beartype **text utility exception.**

    This exception is raised by various functions of the private
    :mod:`beartype._util.text` subpackage.

    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilTextIdentifierException(_BeartypeUtilTextException):
    '''
    Beartype **Python identifier utility exception.**

    This exception is raised by private functions of the private
    :mod:`beartype._util.text.utiltextidentifier` submodule on fatal edge cases.
    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass


class _BeartypeUtilTextVersionException(_BeartypeUtilTextException):
    '''
    Beartype **Python version utility exception.**

    This exception is raised by private functions of the private
    :mod:`beartype._util.text.utiltextversion` submodule on fatal edge cases.
    This exception denotes a critical internal issue and should thus *never* be
    raised -- let alone allowed to percolate up the call stack to end users.
    '''

    pass
