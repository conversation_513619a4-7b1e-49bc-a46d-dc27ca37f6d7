# RAGFlow Agent 对话程序项目

## 项目概述

基于RAGFlow Python SDK开发的Agent对话程序，实现与RAGFlow Agent的实时对话功能。

## 配置信息

- **API Key**: `ragflow-Q4ZjgwOTBhNzI2MTExZjBiZjBiMDI0Mm`
- **Base URL**: `http://117.72.181.138:180/`
- **Agent ID**: `92a162f6710011f097860242ac130006`

## 文件结构

```
├── ragflow_agent_chat.py      # 完整版对话程序（推荐）
├── simple_agent_chat.py       # 简化版对话程序
├── agent_chat_with_config.py  # 使用配置文件的版本
├── test_connection.py         # 连接测试脚本
├── config.py                  # 配置文件
├── requirements.txt           # 依赖包列表
├── run.bat                    # Windows启动脚本
├── run.sh                     # Linux/Mac启动脚本
├── README.md                  # 详细说明文档
└── 项目说明.md               # 项目总结（本文件）
```

## 快速开始

### 1. 安装依赖

```bash
pip install ragflow-sdk
```

### 2. 测试连接

```bash
python test_connection.py
```

### 3. 运行对话程序

**方式一：使用启动脚本（推荐）**
- Windows: 双击 `run.bat`
- Linux/Mac: `./run.sh`

**方式二：直接运行Python脚本**
```bash
# 完整版（推荐）
python ragflow_agent_chat.py

# 简化版
python simple_agent_chat.py

# 配置版
python agent_chat_with_config.py
```

## 程序特性对比

| 特性 | 简化版 | 完整版 | 配置版 |
|------|--------|--------|--------|
| 基本对话 | ✅ | ✅ | ✅ |
| 流式输出 | ✅ | ✅ | ✅ |
| 引用显示 | ✅ | ✅ | ✅ |
| 错误处理 | 基础 | 完整 | 完整 |
| 帮助命令 | ❌ | ✅ | ✅ |
| 清屏功能 | ❌ | ✅ | ✅ |
| 配置文件 | ❌ | ❌ | ✅ |
| 代码行数 | ~80行 | ~200行 | ~180行 |

## 使用示例

```
👤 您: 你好，请介绍一下自己

🤖 Agent: 您好！我是基于RAGFlow的AI助手，可以帮助您解答各种问题...

📚 参考资料:
  [1] introduction.pdf (相似度: 0.856)
      内容: 这是一个关于AI助手的介绍文档...

👤 您: quit
👋 再见！
```

## 支持的命令

- `quit`, `exit`, `q` - 退出程序
- `help`, `h` - 显示帮助信息（完整版和配置版）
- `clear`, `cls` - 清屏（完整版和配置版）
- `Ctrl+C` - 强制退出

## 技术实现

### 核心API调用流程

1. **初始化客户端**
   ```python
   rag_client = RAGFlow(api_key=API_KEY, base_url=BASE_URL)
   ```

2. **获取Agent**
   ```python
   agents = rag_client.list_agents(id=AGENT_ID)
   agent = agents[0]
   ```

3. **创建会话**
   ```python
   session = agent.create_session()
   ```

4. **发送问题**
   ```python
   for response in session.ask(question=question, stream=True):
       print(response.content, end="", flush=True)
   ```

### 关键特性

- **流式输出**: 实时显示Agent回答，提升用户体验
- **引用显示**: 显示回答的参考资料和相似度
- **错误处理**: 完善的异常捕获和错误提示
- **命令支持**: 支持退出、帮助、清屏等命令

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 确认Base URL正确
   - 验证RAGFlow服务状态

2. **认证失败**
   - 检查API Key是否正确
   - 确认API Key未过期

3. **Agent不存在**
   - 验证Agent ID是否正确
   - 确认Agent已创建并发布

4. **依赖包问题**
   ```bash
   pip install --upgrade ragflow-sdk
   ```

### 调试建议

1. 先运行 `test_connection.py` 测试连接
2. 查看详细错误信息
3. 检查配置参数是否正确
4. 确认网络环境稳定

## 扩展功能

可以基于现有代码扩展以下功能：

- 对话历史保存
- 多Agent切换
- 自定义提示词
- 文件上传对话
- 语音输入输出
- Web界面

## 参考资料

- [RAGFlow官方文档](https://ragflow.com.cn/docs/dev/)
- [Python API参考](https://ragflow.com.cn/docs/dev/python_api_reference)
- [RAGFlow GitHub](https://github.com/infiniflow/ragflow)
